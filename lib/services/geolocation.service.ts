"use client"

export interface GeolocationResult {
  latitude: number
  longitude: number
  accuracy: number
}

export interface LocationData {
  location: string
  locationPlaceId: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

export enum GeolocationError {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  POSITION_UNAVAILABLE = 'POSITION_UNAVAILABLE',
  TIMEOUT = 'TIMEOUT',
  NOT_SUPPORTED = 'NOT_SUPPORTED',
  REVERSE_GEOCODING_FAILED = 'REVERSE_GEOCODING_FAILED',
}

export class GeolocationService {
  /**
   * Check if geolocation is supported by the browser
   */
  static isSupported(): boolean {
    return 'geolocation' in navigator
  }

  /**
   * Get current position using browser geolocation API
   */
  static async getCurrentPosition(): Promise<GeolocationResult> {
    return new Promise((resolve, reject) => {
      if (!this.isSupported()) {
        reject(new Error(GeolocationError.NOT_SUPPORTED))
        return
      }

      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 15000, // 15 seconds
        maximumAge: 300000, // 5 minutes cache
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
          })
        },
        (error) => {
          let errorType: GeolocationError
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorType = GeolocationError.PERMISSION_DENIED
              break
            case error.POSITION_UNAVAILABLE:
              errorType = GeolocationError.POSITION_UNAVAILABLE
              break
            case error.TIMEOUT:
              errorType = GeolocationError.TIMEOUT
              break
            default:
              errorType = GeolocationError.POSITION_UNAVAILABLE
          }
          reject(new Error(errorType))
        },
        options
      )
    })
  }

  /**
   * Reverse geocode coordinates to get address using Google Places API
   */
  static async reverseGeocode(latitude: number, longitude: number): Promise<LocationData> {
    try {
      const response = await fetch('/api/places/reverse-geocode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          latitude,
          longitude,
        }),
      })

      if (!response.ok) {
        throw new Error('Reverse geocoding API failed')
      }

      const data = await response.json()
      
      if (!data.location || !data.locationPlaceId) {
        throw new Error('Invalid reverse geocoding response')
      }

      return {
        location: data.location,
        locationPlaceId: data.locationPlaceId,
        coordinates: {
          latitude,
          longitude,
        },
      }
    } catch (error) {
      console.error('Reverse geocoding failed:', error)
      throw new Error(GeolocationError.REVERSE_GEOCODING_FAILED)
    }
  }

  /**
   * Get user location with automatic reverse geocoding
   * This is the main method to use for getting location data
   */
  static async getUserLocation(): Promise<LocationData> {
    try {
      // Step 1: Get coordinates from browser
      const position = await this.getCurrentPosition()
      
      // Step 2: Convert coordinates to address
      const locationData = await this.reverseGeocode(position.latitude, position.longitude)
      
      return locationData
    } catch (error) {
      // Re-throw with proper error type
      throw error
    }
  }

  /**
   * Get user-friendly error message for display
   */
  static getErrorMessage(error: Error): string {
    switch (error.message) {
      case GeolocationError.PERMISSION_DENIED:
        return 'Location access was denied. Please enable location permissions and try again.'
      case GeolocationError.POSITION_UNAVAILABLE:
        return 'Your location could not be determined. Please try again or enter your location manually.'
      case GeolocationError.TIMEOUT:
        return 'Location request timed out. Please try again or enter your location manually.'
      case GeolocationError.NOT_SUPPORTED:
        return 'Geolocation is not supported by your browser. Please enter your location manually.'
      case GeolocationError.REVERSE_GEOCODING_FAILED:
        return 'Could not determine your address. Please enter your location manually.'
      default:
        return 'Could not get your location. Please enter your location manually.'
    }
  }

  /**
   * Check if user has previously denied location permission
   * This helps with UX to show appropriate messaging
   */
  static async checkPermissionStatus(): Promise<'granted' | 'denied' | 'prompt' | 'unsupported'> {
    if (!this.isSupported()) {
      return 'unsupported'
    }

    try {
      // Check if Permissions API is supported
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'geolocation' })
        return permission.state
      }
      
      // Fallback: assume prompt if Permissions API not supported
      return 'prompt'
    } catch (error) {
      // Fallback for browsers that don't support permissions query
      return 'prompt'
    }
  }
}
