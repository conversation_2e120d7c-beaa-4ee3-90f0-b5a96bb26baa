// Client-side email service that calls the server API endpoints

import { Invitation } from "./firebase/invitation-service"

export interface EmailResult {
  success: boolean
  error?: string
  messageId?: string
}

/**
 * Generate an invitation link
 */
export const generateInvitationLink = (invitationId: string, invitationSendId?: string): string => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"

  if (invitationSendId) {
    // For specific email invitations, include the invitation send ID
    return `${baseUrl}/invitation/${invitationId}/${invitationSendId}`
  } else {
    // For general invitation links, use the basic format
    return `${baseUrl}/invitation/${invitationId}`
  }
}

/**
 * Send an invitation email via the server API
 */
export const sendInvitationEmail = async (
  invitation: Invitation,
  _invitationLink: string, // Not used here as the server generates the link
  templateId?: number, // Optional template ID to use
  invitationSendId?: string // Optional invitation send ID for specific email invitations
): Promise<EmailResult> => {
  try {
    // Get a fresh auth token from Firebase Auth
    let token = localStorage.getItem("authToken")

    // Try to get a fresh token if we have access to Firebase Auth
    try {
      const { auth } = await import("@/lib/firebase")
      if (auth.currentUser) {
        token = await auth.currentUser.getIdToken(true) // Force refresh
        // Update localStorage with fresh token
        localStorage.setItem("authToken", token)
      }
    } catch (authError) {
      console.warn("Could not refresh auth token, using stored token:", authError)
    }

    if (!token) {
      return {
        success: false,
        error: "Authentication required to send emails",
      }
    }

    // Call our API endpoint for sending invitation emails
    console.log("Sending invitation email with token:", token ? "Token present" : "No token")

    const response = await fetch("/api/email/invitation", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        invitationId: invitation.id,
        templateId: templateId,
        inviteeEmail: invitation.inviteeEmail,
        invitationSendId: invitationSendId,
      }),
    })

    console.log("API response status:", response.status, response.statusText)

    let data
    try {
      data = await response.json()
    } catch (parseError) {
      console.error("Failed to parse API response:", parseError)
      return {
        success: false,
        error: `API response parsing failed: ${response.status} ${response.statusText}`,
      }
    }

    if (response.ok) {
      return {
        success: true,
        messageId: data.messageId || `email-${Date.now()}`,
      }
    } else {
      console.error("Invitation email sending failed:", {
        status: response.status,
        statusText: response.statusText,
        data: data,
      })

      // Provide more detailed error information
      const errorMessage = data?.error || "Failed to send invitation email"
      const errorDetails = data?.details ? `: ${data.details}` : ""
      const statusInfo = ` (Status: ${response.status})`

      return {
        success: false,
        error: `${errorMessage}${errorDetails}${statusInfo}`,
      }
    }
  } catch (error) {
    console.error("Error sending invitation email:", error)
    return {
      success: false,
      error: "An unexpected error occurred while sending the invitation email",
    }
  }
}
