# Hybrid Image Compression Implementation Guide

## Overview

This guide explains how to use the new hybrid image compression system that combines client-side pre-compression with server-side optimization.

## Quick Start

### 1. Basic Usage

```tsx
import { ImageUploadWithCompression } from '@/components/ui/image-upload-with-compression'

function MyComponent() {
  const handleFileSelect = (file: File) => {
    // File is already compressed and ready for upload
    console.log('Compressed file:', file)
  }

  return (
    <ImageUploadWithCompression
      preset="profilePicture"
      onFileSelect={handleFileSelect}
      onError={(error) => console.error(error)}
    />
  )
}
```

### 2. Using the Hook Directly

```tsx
import { useProfilePictureCompression } from '@/hooks/use-image-compression'

function CustomUpload() {
  const { compress, isCompressing, progress, result } = useProfilePictureCompression()

  const handleFileInput = async (file: File) => {
    const compressionResult = await compress(file)
    if (compressionResult.success) {
      // Upload the compressed file
      uploadToServer(compressionResult.file)
    }
  }

  return (
    <div>
      <input type="file" onChange={(e) => handleFileInput(e.target.files[0])} />
      {isCompressing && <div>Progress: {progress?.progress}%</div>}
    </div>
  )
}
```

## Available Presets

### Profile Pictures
- **Client-side**: Compress to ~2MB, max 800px, WebP format
- **Server-side**: Resize to 400x400px, 85% quality, WebP format

### Travel Details
- **Client-side**: Compress to ~3MB, max 1200px, WebP format  
- **Server-side**: Resize to max 1200px width, 80% quality, WebP format

### General
- **Client-side**: Compress to ~2MB, max 1920px, WebP format
- **Server-side**: No specific optimization (use custom settings)

## Server Action Integration

The server actions have been updated to automatically handle optimization:

```typescript
// Example: Profile picture upload
const result = await uploadProfilePictureAction(formData)
if (result.success) {
  console.log('Uploaded URL:', result.url) // Already optimized WebP image
}
```

## File Size Limits

- **Maximum upload**: 50MB (increased from 1MB)
- **Client compression target**: 2-3MB depending on preset
- **Final optimized size**: Typically 100-500KB for profile pictures

## Compression Flow

1. **File Selection**: User selects image up to 50MB
2. **Validation**: Check file type and size limits
3. **Client Compression**: If file > 2MB, compress using WebWorker
4. **Upload**: Send compressed file to server
5. **Server Optimization**: Final resize, format conversion, quality adjustment
6. **Storage**: Save optimized WebP image to Vercel Blob

## Error Handling

### Client-Side Errors
- Invalid file type
- File too large (>50MB)
- Compression failure
- Browser compatibility issues

### Server-Side Errors
- Image validation failure
- Sharp processing errors
- Vercel Blob upload errors
- Network issues

### Fallback Strategy
- If client compression fails and file < 10MB: Upload directly
- If client compression fails and file > 10MB: Show error
- Server optimization always runs as final step

## Performance Considerations

### Client-Side
- Uses WebWorkers to avoid blocking UI
- Progress tracking with estimated time
- Typical compression time: 5-30 seconds for large files

### Server-Side
- Sharp processing adds ~1-3 seconds
- Optimized for Vercel serverless functions
- Automatic memory management

## Browser Compatibility

### Supported Browsers
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Features Used
- WebWorkers (for non-blocking compression)
- File API
- Canvas API (for image processing)
- Blob/ArrayBuffer APIs

## Customization

### Custom Compression Options

```tsx
import { useImageCompression } from '@/hooks/use-image-compression'

const { compress } = useImageCompression({
  customOptions: {
    maxSizeMB: 1.5,
    maxWidthOrHeight: 1000,
    initialQuality: 0.9
  }
})
```

### Custom Server Optimization

```typescript
import { optimizeImage } from '@/lib/utils/server-image-optimization'

const customOptions = {
  width: 600,
  height: 400,
  quality: 90,
  format: 'jpeg' as const,
  fit: 'cover' as const
}

const result = await optimizeImage(inputBuffer, customOptions)
```

## Monitoring and Debugging

### Client-Side Logging
```typescript
const { compress } = useImageCompression({
  onProgress: (progress) => {
    console.log(`${progress.stage}: ${progress.progress}%`)
  },
  onComplete: (result) => {
    console.log('Compression savings:', result.compressionRatio)
  }
})
```

### Server-Side Logging
Server actions automatically log:
- Original file size
- Optimized file size
- Compression savings
- Processing time

## Migration from Old System

### Before (1MB limit)
```tsx
// Old way - direct upload with 1MB limit
<input type="file" accept="image/*" />
```

### After (50MB with compression)
```tsx
// New way - hybrid compression with 50MB limit
<ImageUploadWithCompression
  preset="profilePicture"
  onFileSelect={handleCompressedFile}
/>
```

### Server Action Changes
- File size validation updated to 50MB
- Added Sharp optimization
- WebP format conversion
- Enhanced error messages

## Best Practices

1. **Choose appropriate presets** for your use case
2. **Show progress indicators** for better UX
3. **Handle errors gracefully** with clear messages
4. **Test on mobile devices** for performance
5. **Monitor compression ratios** to optimize settings
6. **Use WebP format** for best compression
7. **Implement fallbacks** for older browsers

## Troubleshooting

### Common Issues

**"Compression failed" error**
- Check browser compatibility
- Verify file is a valid image
- Try with smaller file size

**"File too large" error**
- Ensure file is under 50MB
- Check if client compression is working

**Server optimization errors**
- Verify Sharp is installed correctly
- Check server memory limits
- Validate image buffer integrity

### Performance Issues

**Slow compression on mobile**
- Reduce `maxWidthOrHeight` setting
- Lower `initialQuality` value
- Consider disabling WebWorkers on very old devices

**Server timeout errors**
- Optimize Sharp settings
- Reduce image dimensions
- Check Vercel function timeout limits
