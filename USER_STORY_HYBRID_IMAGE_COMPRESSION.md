# User Story: Hybrid Image Compression System

## Epic

Image Upload Optimization

## User Story

**As a** user uploading profile pictures or travel detail images  
**I want** to be able to upload larger image files (up to 50MB) that are automatically optimized  
**So that** I can share high-quality images without worrying about file size limits while the app maintains good performance

## Problem Statement

Currently, users are limited to 1MB image uploads, which is restrictive for modern high-resolution photos from smartphones and cameras. This forces users to manually compress images before uploading, creating friction in the user experience.

## Solution Overview

Implement a hybrid image compression system that:

1. **Client-side**: Pre-compresses very large images to reduce upload time and bandwidth
2. **Server-side**: Performs final optimization and format standardization using Sharp
3. **User Experience**: Allows uploads up to 50MB with progress indicators during processing

## Acceptance Criteria

### Functional Requirements

#### AC1: Client-Side Pre-Compression

- **Given** a user selects an image file larger than 2MB
- **When** they initiate the upload
- **Then** the image should be compressed client-side before upload
- **And** compression should target ~2MB for upload efficiency
- **And** processing time should not exceed 120 seconds on modern devices
- **And** a progress indicator should show compression status

#### AC2: Server-Side Final Optimization

- **Given** an image has been uploaded (pre-compressed or original)
- **When** the server processes the image
- **Then** it should apply final compression using Sharp
- **And** generate optimized versions (e.g., 800px width for profiles, 1200px for travel details)
- **And** convert to WebP format for better compression
- **And** maintain aspect ratio
- **And** ensure final file size is reasonable for web delivery

#### AC3: File Size Limits

- **Given** a user attempts to upload an image
- **When** the file size exceeds 50MB
- **Then** they should receive a clear error message
- **And** be guided on how to reduce file size
- **When** the file size is under 50MB
- **Then** the upload should proceed with appropriate compression

#### AC4: Format Support

- **Given** a user uploads an image
- **When** the file is in JPEG, PNG, WebP, or HEIC format
- **Then** it should be accepted and processed
- **When** the file is in an unsupported format
- **Then** they should receive a clear error message with supported formats

#### AC5: Fallback Handling

- **Given** client-side compression fails or is unsupported
- **When** the original file is under 10MB
- **Then** it should upload directly to server for processing
- **When** the original file is over 10MB and client compression fails
- **Then** show an error asking user to manually reduce file size

### Technical Requirements

#### AC6: Performance Standards

- **Given** normal network conditions
- **When** uploading a 5MB image
- **Then** total processing time should not exceed 30 seconds
- **When** uploading a 20MB image
- **Then** total processing time should not exceed 90 seconds

#### AC7: Error Handling

- **Given** any step in the compression pipeline fails
- **When** an error occurs
- **Then** provide specific, actionable error messages
- **And** allow users to retry the upload
- **And** log detailed error information for debugging

#### AC8: Progress Feedback

- **Given** an image is being processed
- **When** compression is in progress
- **Then** show progress indicators for:
  - Client-side compression progress
  - Upload progress
  - Server-side processing status
- **And** estimated time remaining when possible

### User Experience Requirements

#### AC9: Mobile Optimization

- **Given** a user is on a mobile device
- **When** they upload images
- **Then** compression should be optimized for mobile performance
- **And** not cause browser crashes or excessive battery drain
- **And** work reliably on iOS Safari and Android Chrome

#### AC10: Backward Compatibility

- **Given** existing upload functionality
- **When** the new system is deployed
- **Then** all existing upload endpoints should continue working
- **And** existing uploaded images should remain accessible
- **And** no breaking changes to the API

## Implementation Areas

### Files to Modify

1. **Client-side compression components**

   - Profile picture upload components
   - Travel details upload components
   - Shared image upload utilities

2. **Server actions**

   - `app/signup/actions/upload-profile-picture.ts`
   - `app/(authenticated)/settings/actions/upload-blob.ts`
   - `app/(authenticated)/trips/[id]/actions/upload-travel-details.ts`

3. **Dependencies**
   - Add `browser-image-compression` for client-side
   - Add `sharp` for server-side optimization

### Technical Specifications

#### Client-Side Compression Settings

```typescript
const compressionOptions = {
  maxSizeMB: 2, // Target 2MB for upload
  maxWidthOrHeight: 1920, // Max dimension
  useWebWorker: true, // Don't block UI
  fileType: "image/webp", // Prefer WebP
  initialQuality: 0.8, // Starting quality
}
```

#### Server-Side Optimization Settings

```typescript
const serverOptimization = {
  profilePictures: {
    width: 400,
    height: 400,
    quality: 85,
    format: "webp",
  },
  travelDetails: {
    width: 1200,
    quality: 80,
    format: "webp",
  },
}
```

## Success Metrics

- Increase in successful image uploads (target: 95%+ success rate)
- Reduction in user-reported upload issues
- Improved user satisfaction with image upload experience
- Maintained or improved page load performance
- Reduced bandwidth usage for image delivery

## Risks and Mitigations

- **Risk**: Client-side compression fails on older devices
  - **Mitigation**: Fallback to direct upload for smaller files
- **Risk**: Server processing time increases
  - **Mitigation**: Implement async processing with status updates
- **Risk**: Increased server costs due to Sharp processing
  - **Mitigation**: Monitor usage and optimize compression settings

## Dependencies

- `browser-image-compression` npm package
- `sharp` npm package
- Vercel Blob storage (existing)
- Current upload infrastructure (existing)

## Implementation Status ✅

### Completed Components

#### 1. Core Utilities

- ✅ **Client-side compression utility** (`lib/utils/image-compression.ts`)

  - Supports up to 50MB file uploads
  - Configurable compression presets (profile, travel details, general)
  - Progress tracking with estimated time remaining
  - File validation and format support (JPEG, PNG, WebP, HEIC)
  - Compression statistics and savings calculation

- ✅ **Server-side optimization utility** (`lib/utils/server-image-optimization.ts`)
  - Sharp-based image processing
  - Multiple optimization presets
  - Format conversion to WebP
  - Metadata extraction and validation
  - Buffer/File conversion utilities

#### 2. React Integration

- ✅ **Image compression hook** (`hooks/use-image-compression.ts`)

  - Reusable React hook with progress tracking
  - Specialized hooks for profile pictures and travel details
  - Error handling and completion callbacks
  - File validation and compression detection

- ✅ **Upload component** (`components/ui/image-upload-with-compression.tsx`)
  - Drag-and-drop interface
  - Real-time compression progress
  - Preview functionality
  - Compression statistics display
  - Error handling and validation

#### 3. Server Actions Updated

- ✅ **Profile picture upload** (`app/signup/actions/upload-profile-picture.ts`)
- ✅ **Settings blob upload** (`app/(authenticated)/settings/actions/upload-blob.ts`)
- ✅ **Travel details upload** (`app/(authenticated)/trips/[id]/actions/upload-travel-details.ts`)

All server actions now include:

- 50MB file size limit (up from 1MB)
- Server-side image optimization using Sharp
- WebP format conversion
- Enhanced error handling
- Compression logging and statistics

#### 4. Dependencies Installed

- ✅ `browser-image-compression@2.0.2` - Client-side compression
- ✅ `sharp@0.34.3` - Server-side optimization

#### 5. Example Implementation

- ✅ **Example component** (`components/examples/image-upload-example.tsx`)
  - Demonstrates complete upload flow
  - Shows different presets in action
  - Implementation guidance and notes

### Technical Specifications Implemented

#### Client-Side Compression Settings

```typescript
profilePicture: {
  maxSizeMB: 2,
  maxWidthOrHeight: 800,
  useWebWorker: true,
  fileType: 'image/webp',
  initialQuality: 0.85
}

travelDetails: {
  maxSizeMB: 3,
  maxWidthOrHeight: 1200,
  useWebWorker: true,
  fileType: 'image/webp',
  initialQuality: 0.8
}
```

#### Server-Side Optimization Settings

```typescript
profilePicture: {
  width: 400,
  height: 400,
  quality: 85,
  format: 'webp',
  fit: 'cover'
}

travelDetails: {
  width: 1200,
  quality: 80,
  format: 'webp',
  fit: 'inside'
}
```

## Definition of Done

- [x] All acceptance criteria are met and implemented
- [x] Client-side compression works with WebWorkers
- [x] Server-side optimization using Sharp
- [x] Error handling covers all failure scenarios
- [x] File size limits increased to 50MB
- [x] Progress tracking and user feedback
- [x] Fallback handling for compression failures
- [x] Documentation and examples created
- [ ] QA testing on desktop and mobile browsers
- [ ] Performance benchmarking
- [ ] Production deployment
