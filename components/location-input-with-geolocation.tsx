"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { MapPin, Loader2, AlertCircle } from "lucide-react"
import { DestinationAutocomplete } from "@/app/(authenticated)/trips/create/components/destination-autocomplete"
import { GeolocationService, GeolocationError } from "@/lib/services/geolocation.service"
import { toast } from "@/components/ui/use-toast"
import {
  Alert,
  AlertDescription,
} from "@/components/ui/alert"

interface LocationInputWithGeolocationProps {
  value: string
  onChange: (value: string, placeId?: string) => void
  placeholder?: string
  required?: boolean
  allowUnauthenticated?: boolean
  label?: string
  showGeolocationButton?: boolean
}

export function LocationInputWithGeolocation({
  value,
  onChange,
  placeholder = "Enter your location",
  required = false,
  allowUnauthenticated = false,
  label = "Location",
  showGeolocationButton = true,
}: LocationInputWithGeolocationProps) {
  const [gettingLocation, setGettingLocation] = useState(false)
  const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'prompt' | 'unsupported'>('prompt')
  const [geolocationError, setGeolocationError] = useState<string | null>(null)

  // Check permission status on mount
  useEffect(() => {
    const checkPermission = async () => {
      const status = await GeolocationService.checkPermissionStatus()
      setPermissionStatus(status)
    }
    
    if (showGeolocationButton) {
      checkPermission()
    }
  }, [showGeolocationButton])

  const handleUseMyLocation = async () => {
    setGettingLocation(true)
    setGeolocationError(null)

    try {
      const locationData = await GeolocationService.getUserLocation()
      
      // Update the input with the location data
      onChange(locationData.location, locationData.locationPlaceId)
      
      // Show success toast
      toast({
        title: "Location detected",
        description: `Set your location to ${locationData.location}`,
      })

      // Update permission status
      setPermissionStatus('granted')
      
    } catch (error) {
      const errorMessage = GeolocationService.getErrorMessage(error as Error)
      setGeolocationError(errorMessage)
      
      // Update permission status if it was denied
      if ((error as Error).message === GeolocationError.PERMISSION_DENIED) {
        setPermissionStatus('denied')
      }
      
      // Show error toast
      toast({
        title: "Location access failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setGettingLocation(false)
    }
  }

  const getButtonText = () => {
    if (gettingLocation) return "Getting location..."
    
    switch (permissionStatus) {
      case 'denied':
        return "Location access denied"
      case 'unsupported':
        return "Location not supported"
      case 'granted':
        return "Use my location"
      default:
        return "Use my location"
    }
  }

  const getButtonVariant = () => {
    switch (permissionStatus) {
      case 'denied':
      case 'unsupported':
        return "secondary" as const
      default:
        return "outline" as const
    }
  }

  const isButtonDisabled = () => {
    return gettingLocation || permissionStatus === 'unsupported'
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label>{label} {required && <span className="text-red-500">*</span>}</Label>
        {showGeolocationButton && GeolocationService.isSupported() && (
          <Button
            type="button"
            variant={getButtonVariant()}
            size="sm"
            onClick={handleUseMyLocation}
            disabled={isButtonDisabled()}
            className="text-xs"
          >
            {gettingLocation ? (
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            ) : (
              <MapPin className="h-3 w-3 mr-1" />
            )}
            {getButtonText()}
          </Button>
        )}
      </div>

      {/* Main autocomplete input */}
      <DestinationAutocomplete
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        allowUnauthenticated={allowUnauthenticated}
        label="" // We handle the label above
      />

      {/* Error message for geolocation */}
      {geolocationError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {geolocationError}
          </AlertDescription>
        </Alert>
      )}

      {/* Permission status hints */}
      {showGeolocationButton && permissionStatus === 'denied' && !geolocationError && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Location access was previously denied. You can enable it in your browser settings or enter your location manually.
          </AlertDescription>
        </Alert>
      )}

      {showGeolocationButton && permissionStatus === 'unsupported' && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Your browser doesn't support location detection. Please enter your location manually.
          </AlertDescription>
        </Alert>
      )}

      {/* Help text */}
      <p className="text-xs text-muted-foreground">
        {showGeolocationButton && GeolocationService.isSupported() 
          ? "Click 'Use my location' for automatic detection, or type your location manually."
          : "Enter your location to get personalized travel suggestions."
        }
      </p>
    </div>
  )
}
