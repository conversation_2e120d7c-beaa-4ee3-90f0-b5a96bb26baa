"use client"

import { Crown } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON><PERSON>, Too<PERSON>ipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface ProBadgeProps {
  className?: string
  size?: "sm" | "md" | "lg"
  variant?: "icon" | "text" | "full"
  showTooltip?: boolean
}

export function ProBadge({ 
  className, 
  size = "md", 
  variant = "icon", 
  showTooltip = true 
}: ProBadgeProps) {
  const sizeClasses = {
    sm: {
      container: "h-5 w-5 p-0.5",
      icon: "h-3 w-3",
      text: "text-xs px-1.5 py-0.5",
      full: "px-2 py-1 text-xs"
    },
    md: {
      container: "h-6 w-6 p-1",
      icon: "h-4 w-4",
      text: "text-sm px-2 py-1",
      full: "px-3 py-1.5 text-sm"
    },
    lg: {
      container: "h-8 w-8 p-1.5",
      icon: "h-5 w-5",
      text: "text-base px-3 py-1.5",
      full: "px-4 py-2 text-base"
    }
  }

  const renderBadge = () => {
    switch (variant) {
      case "icon":
        return (
          <div className={cn(
            "bg-gradient-to-r from-[#FFD54F] to-[#FFC107] rounded-full flex items-center justify-center shadow-lg",
            sizeClasses[size].container,
            className
          )}>
            <Crown className={cn("text-white", sizeClasses[size].icon)} />
          </div>
        )
      
      case "text":
        return (
          <div className={cn(
            "bg-gradient-to-r from-[#FFD54F] to-[#FFC107] rounded-full flex items-center justify-center font-semibold text-white shadow-lg",
            sizeClasses[size].text,
            className
          )}>
            PRO
          </div>
        )
      
      case "full":
        return (
          <div className={cn(
            "bg-gradient-to-r from-[#FFD54F] to-[#FFC107] rounded-full flex items-center gap-1.5 font-semibold text-white shadow-lg",
            sizeClasses[size].full,
            className
          )}>
            <Crown className={cn("text-white", sizeClasses[size].icon)} />
            <span>PRO</span>
          </div>
        )
      
      default:
        return null
    }
  }

  const badge = renderBadge()

  if (!showTooltip) {
    return badge
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="inline-flex shrink-0">
            {badge}
          </span>
        </TooltipTrigger>
        <TooltipContent side="bottom" align="center" sideOffset={8}>
          <p className="text-xs">Pro Subscriber</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
