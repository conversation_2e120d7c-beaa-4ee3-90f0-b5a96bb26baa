'use client'

import React, { useState, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Upload, Image as ImageIcon, X, CheckCircle } from 'lucide-react'
import { useImageCompression } from '@/hooks/use-image-compression'
import { formatFileSize, getCompressionSavings } from '@/lib/utils/image-compression'
import { cn } from '@/lib/utils'

export interface ImageUploadWithCompressionProps {
  onFileSelect: (file: File) => void
  onUploadComplete?: (url: string) => void
  onError?: (error: string) => void
  preset?: 'profilePicture' | 'travelDetails' | 'general'
  className?: string
  disabled?: boolean
  accept?: string
  maxFiles?: number
  showPreview?: boolean
  showCompressionStats?: boolean
}

export function ImageUploadWithCompression({
  onFileSelect,
  onUploadComplete,
  onError,
  preset = 'general',
  className,
  disabled = false,
  accept = 'image/*',
  maxFiles = 1,
  showPreview = true,
  showCompressionStats = true
}: ImageUploadWithCompressionProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [compressionComplete, setCompressionComplete] = useState(false)

  const {
    compress,
    isCompressing,
    progress,
    result,
    reset,
    validateFile,
    needsCompression
  } = useImageCompression({
    preset,
    onProgress: (progressData) => {
      // Progress is handled by the hook's internal state
    },
    onComplete: (compressionResult) => {
      if (compressionResult.success && compressionResult.file) {
        setCompressionComplete(true)
        onFileSelect(compressionResult.file)
      }
    },
    onError: (error) => {
      onError?.(error)
    }
  })

  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]
    setSelectedFile(file)
    setCompressionComplete(false)
    reset()

    // Create preview URL
    if (showPreview) {
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
    }

    // Validate file
    const validation = validateFile(file)
    if (!validation.valid) {
      onError?.(validation.error || 'Invalid file')
      return
    }

    // Check if compression is needed
    if (!needsCompression(file)) {
      // File is already small enough, use as-is
      setCompressionComplete(true)
      onFileSelect(file)
      return
    }

    // Start compression
    await compress(file)
  }, [compress, validateFile, needsCompression, onFileSelect, onError, showPreview, reset])

  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(event.target.files)
  }, [handleFileSelect])

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    handleFileSelect(event.dataTransfer.files)
  }, [handleFileSelect])

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setCompressionComplete(false)
    reset()
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [reset])

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  // Calculate compression stats
  const compressionStats = result && result.success && result.originalSize && result.compressedSize
    ? getCompressionSavings(result.originalSize, result.compressedSize)
    : null

  return (
    <div className={cn('space-y-4', className)}>
      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleInputChange}
        className="hidden"
        disabled={disabled}
        multiple={maxFiles > 1}
      />

      {/* Drop Zone */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
          'hover:border-primary/50 hover:bg-primary/5',
          disabled && 'opacity-50 cursor-not-allowed',
          selectedFile && 'border-primary bg-primary/5'
        )}
      >
        {!selectedFile ? (
          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 text-muted-foreground">
              <Upload className="w-full h-full" />
            </div>
            <div>
              <Button
                type="button"
                variant="outline"
                onClick={openFileDialog}
                disabled={disabled}
              >
                Choose Image
              </Button>
              <p className="text-sm text-muted-foreground mt-2">
                or drag and drop an image here
              </p>
              <p className="text-xs text-muted-foreground">
                Maximum file size: 50MB
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Preview */}
            {showPreview && previewUrl && (
              <div className="relative inline-block">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="max-w-32 max-h-32 object-cover rounded-lg"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                  onClick={clearSelection}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}

            {/* File Info */}
            <div className="text-sm">
              <p className="font-medium">{selectedFile.name}</p>
              <p className="text-muted-foreground">
                {formatFileSize(selectedFile.size)}
              </p>
            </div>

            {/* Compression Status */}
            {isCompressing && progress && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{progress.message}</span>
                  <span>{Math.round(progress.progress)}%</span>
                </div>
                <Progress value={progress.progress} className="h-2" />
                {progress.estimatedTimeRemaining && (
                  <p className="text-xs text-muted-foreground">
                    Estimated time remaining: {Math.round(progress.estimatedTimeRemaining / 1000)}s
                  </p>
                )}
              </div>
            )}

            {/* Compression Complete */}
            {compressionComplete && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Ready for upload</span>
              </div>
            )}

            {/* Compression Stats */}
            {showCompressionStats && compressionStats && (
              <Alert>
                <ImageIcon className="h-4 w-4" />
                <AlertDescription>
                  <div className="text-sm space-y-1">
                    <p>
                      <strong>Compression:</strong> {formatFileSize(result!.originalSize!)} → {formatFileSize(result!.compressedSize!)}
                    </p>
                    <p>
                      <strong>Savings:</strong> {formatFileSize(compressionStats.savedBytes)} ({compressionStats.savedPercentage.toFixed(1)}%)
                    </p>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {result && !result.success && (
        <Alert variant="destructive">
          <AlertDescription>
            {result.error}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
