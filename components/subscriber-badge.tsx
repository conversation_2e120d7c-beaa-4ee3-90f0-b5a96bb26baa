"use client"

import { Crown } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface SubscriberBadgeProps {
  className?: string
}

export function SubscriberBadge({ className = "" }: SubscriberBadgeProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={`inline-flex shrink-0 ${className}`}>
            <div className="bg-gradient-to-r from-[#FFD54F] to-[#FFC107] rounded-full p-0.5">
              <Crown className="h-3 w-3 text-white" />
            </div>
          </span>
        </TooltipTrigger>
        <TooltipContent side="bottom" align="center" sideOffset={8}>
          <p className="text-xs">Pro Subscriber</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
