'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ImageUploadWithCompression } from '@/components/ui/image-upload-with-compression'
import { toast } from 'sonner'

/**
 * Example component demonstrating how to use the ImageUploadWithCompression component
 * This shows the complete flow: client-side compression → server upload → success
 */
export function ImageUploadExample() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null)

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    setUploadedUrl(null) // Clear previous upload
    toast.success('Image compressed and ready for upload!')
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    
    try {
      // Create FormData for the server action
      const formData = new FormData()
      formData.append('file', selectedFile)

      // Example: Upload profile picture (you can change this to any upload action)
      const response = await fetch('/api/upload-example', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()
      
      if (result.success) {
        setUploadedUrl(result.url)
        toast.success('Image uploaded successfully!')
      } else {
        throw new Error(result.error || 'Upload failed')
      }

    } catch (error) {
      console.error('Upload error:', error)
      toast.error(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setIsUploading(false)
    }
  }

  const handleError = (error: string) => {
    toast.error(error)
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Image Upload with Compression</CardTitle>
          <CardDescription>
            Upload images up to 50MB. They will be automatically compressed on the client-side 
            and then optimized on the server before storage.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Profile Picture Upload Example */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Profile Picture Upload</h3>
            <ImageUploadWithCompression
              preset="profilePicture"
              onFileSelect={handleFileSelect}
              onError={handleError}
              showPreview={true}
              showCompressionStats={true}
            />
          </div>

          {/* Upload Button */}
          {selectedFile && (
            <div className="flex gap-2">
              <Button 
                onClick={handleUpload}
                disabled={isUploading}
                className="flex-1"
              >
                {isUploading ? 'Uploading...' : 'Upload to Server'}
              </Button>
              <Button 
                variant="outline"
                onClick={() => {
                  setSelectedFile(null)
                  setUploadedUrl(null)
                }}
              >
                Clear
              </Button>
            </div>
          )}

          {/* Upload Result */}
          {uploadedUrl && (
            <div className="space-y-2">
              <h4 className="font-medium text-green-600">Upload Successful!</h4>
              <div className="p-3 bg-green-50 rounded-lg">
                <p className="text-sm text-green-800">
                  <strong>URL:</strong> {uploadedUrl}
                </p>
                <img 
                  src={uploadedUrl} 
                  alt="Uploaded" 
                  className="mt-2 max-w-32 max-h-32 object-cover rounded"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Travel Details Upload</CardTitle>
          <CardDescription>
            Example for travel detail images with different compression settings.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ImageUploadWithCompression
            preset="travelDetails"
            onFileSelect={(file) => {
              console.log('Travel details file selected:', file)
              toast.success('Travel details image ready!')
            }}
            onError={handleError}
            showPreview={true}
            showCompressionStats={true}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Implementation Notes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div>
            <h4 className="font-medium">Client-Side Compression:</h4>
            <ul className="list-disc list-inside text-muted-foreground space-y-1">
              <li>Files larger than 2MB are automatically compressed</li>
              <li>Uses WebWorkers to avoid blocking the UI</li>
              <li>Shows real-time progress and estimated time</li>
              <li>Converts to WebP format for better compression</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium">Server-Side Optimization:</h4>
            <ul className="list-disc list-inside text-muted-foreground space-y-1">
              <li>Profile pictures: resized to 400x400px, 85% quality</li>
              <li>Travel details: resized to max 1200px width, 80% quality</li>
              <li>Final conversion to WebP format</li>
              <li>Validation and error handling</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium">Fallback Handling:</h4>
            <ul className="list-disc list-inside text-muted-foreground space-y-1">
              <li>If client compression fails, files under 10MB upload directly</li>
              <li>Server-side optimization always runs as final step</li>
              <li>Clear error messages for unsupported formats or oversized files</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
