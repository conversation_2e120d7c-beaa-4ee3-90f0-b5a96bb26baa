import { NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { latitude, longitude } = await request.json()

    if (!latitude || !longitude) {
      return NextResponse.json(
        { error: "Latitude and longitude are required" },
        { status: 400 }
      )
    }

    const apiKey = process.env.GOOGLE_PLACES_API_KEY
    if (!apiKey) {
      console.error("Google Places API key not configured")
      return NextResponse.json(
        { error: "Google Places API not configured" },
        { status: 500 }
      )
    }

    // Use Google Geocoding API to reverse geocode coordinates
    const geocodingUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`
    
    const response = await fetch(geocodingUrl)
    const data = await response.json()

    if (!response.ok || data.status !== "OK") {
      console.error("Google Geocoding API error:", data)
      return NextResponse.json(
        { error: "Failed to reverse geocode coordinates" },
        { status: 500 }
      )
    }

    if (!data.results || data.results.length === 0) {
      return NextResponse.json(
        { error: "No results found for the given coordinates" },
        { status: 404 }
      )
    }

    // Get the first result (most accurate)
    const result = data.results[0]
    
    // Extract formatted address and place ID
    const location = result.formatted_address
    const locationPlaceId = result.place_id

    if (!location || !locationPlaceId) {
      return NextResponse.json(
        { error: "Invalid geocoding result" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      location,
      locationPlaceId,
      coordinates: {
        latitude,
        longitude,
      },
      // Include additional data that might be useful
      addressComponents: result.address_components,
      geometry: result.geometry,
    })

  } catch (error) {
    console.error("Reverse geocoding error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
