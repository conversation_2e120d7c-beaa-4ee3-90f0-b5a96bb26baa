import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"

/**
 * API route to check if a user exists by email
 * This route does not require authentication and only returns existence status
 * Used for invitation flow to determine redirect behavior
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = body

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: "Invalid email format" }, { status: 400 })
    }

    try {
      const { adminAuth } = await getAdminInstance()
      
      if (!adminAuth) {
        console.error("Firebase Admin Auth is not initialized")
        return NextResponse.json({ error: "Internal server error" }, { status: 500 })
      }

      // Try to get user by email
      await adminAuth.getUserByEmail(email)
      
      // User exists
      return NextResponse.json({ exists: true })
    } catch (error: any) {
      // User not found or other error
      if (error.code === 'auth/user-not-found') {
        return NextResponse.json({ exists: false })
      }
      
      // For other errors, assume user doesn't exist for security
      console.warn("Error checking user existence:", error)
      return NextResponse.json({ exists: false })
    }
  } catch (error) {
    console.error("Error in check-user API route:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
