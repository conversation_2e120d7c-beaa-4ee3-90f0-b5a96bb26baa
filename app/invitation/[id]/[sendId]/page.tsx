"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { PageLoading } from "@/components/page-loading"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle } from "lucide-react"

/**
 * Public invitation page for specific invitation sends
 * This page handles redirect logic for invitations sent to specific emails
 * URL format: /invitation/{invitationId}/{invitationSendId}
 */
export default function SpecificInvitationRedirectPage() {
  const params = useParams()
  const router = useRouter()
  const { user, loading: authLoading } = useAuthStatus()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const handleSpecificInvitationFlow = async () => {
      if (authLoading) return

      const invitationId = params.id as string
      const invitationSendId = params.sendId as string
      
      if (!invitationId || !invitationSendId) {
        setError("Invalid invitation link.")
        setLoading(false)
        return
      }

      try {
        // If user is already logged in, redirect to authenticated squad invitation page
        if (user) {
          console.log("User is logged in, redirecting to authenticated squad invitation page")
          router.push(`/squad-invitation/${invitationId}?sendId=${invitationSendId}`)
          return
        }

        // User is not logged in, get specific invitation send data
        const response = await fetch(`/api/invitation/public/${invitationId}/${invitationSendId}`)
        
        if (!response.ok) {
          if (response.status === 404) {
            setError("Invitation not found or has been cancelled.")
          } else if (response.status === 410) {
            setError("This invitation has expired or is no longer active.")
          } else {
            setError("Unable to process this invitation. Please try again later.")
          }
          setLoading(false)
          return
        }

        const invitationData = await response.json()
        
        // For specific invitation sends, we have the exact email
        if (invitationData.type === "invitation_send" && invitationData.email) {
          console.log("Specific invitation send detected, checking user existence")
          
          const userCheckResponse = await fetch("/api/invitation/check-user", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ email: invitationData.email }),
          })

          if (userCheckResponse.ok) {
            const { exists } = await userCheckResponse.json()
            
            if (exists) {
              // User exists, redirect to login with pre-filled email
              console.log("User exists, redirecting to login")
              router.push(
                `/login?callback=${encodeURIComponent(`/squad-invitation/${invitationId}?sendId=${invitationSendId}`)}&email=${encodeURIComponent(invitationData.email)}`
              )
            } else {
              // User doesn't exist, redirect to signup with pre-filled email
              console.log("User doesn't exist, redirecting to signup")
              router.push(
                `/signup?callback=${encodeURIComponent(`/squad-invitation/${invitationId}?sendId=${invitationSendId}`)}&invited_email=${encodeURIComponent(invitationData.email)}`
              )
            }
          } else {
            // Error checking user, default to signup
            console.log("Error checking user existence, defaulting to signup")
            router.push(
              `/signup?callback=${encodeURIComponent(`/squad-invitation/${invitationId}?sendId=${invitationSendId}`)}&invited_email=${encodeURIComponent(invitationData.email)}`
            )
          }
        } else {
          setError("Invalid invitation format.")
          setLoading(false)
        }
      } catch (error) {
        console.error("Error processing specific invitation:", error)
        setError("Unable to process this invitation. Please try again later.")
        setLoading(false)
      }
    }

    handleSpecificInvitationFlow()
  }, [params.id, params.sendId, user, authLoading, router])

  if (authLoading || loading) {
    return <PageLoading message="Processing invitation..." />
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invitation Error</CardTitle>
            <CardDescription>There was a problem with this invitation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-destructive/10 p-2 rounded-full">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <p className="text-destructive">{error}</p>
            </div>
            <p className="text-muted-foreground mt-2">
              Please contact the squad leader for assistance or try again later.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // This should not be reached as we redirect in useEffect
  return <PageLoading message="Redirecting..." />
}
