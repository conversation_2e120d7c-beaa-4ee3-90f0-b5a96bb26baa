"use client"

import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Send, Crown } from "lucide-react"
import { UserMention } from "./user-mention"
import { useSendMessage } from "@/lib/domains/message/message.hooks"
import { useCreateMessageMentionNotifications } from "@/lib/domains/notification/notification.hooks"
import { MessageCreateData } from "@/lib/domains/message/message.types"
import { useRouter } from "next/navigation"
import { useHasTripChat } from "@/lib/domains/user-subscription/user-subscription.hooks"

interface MessageInputProps {
  tripId: string
  tripName: string
  attendees: Array<{
    id: string
    displayName: string
    photoURL?: string
  }>
  currentUser: {
    id: string
    displayName: string
    photoURL?: string
  }
  canSendMessages?: boolean
  tripStatus?: string
}

export function MessageInput({
  tripId,
  tripName,
  attendees,
  currentUser,
  canSendMessages = true,
  tripStatus,
}: MessageInputProps) {
  const router = useRouter()
  const hasTripChat = useHasTripChat()
  const [content, setContent] = useState("")
  const [showMentions, setShowMentions] = useState(false)
  const [mentionQuery, setMentionQuery] = useState("")
  const [mentionPosition, setMentionPosition] = useState(0)
  const [mentionMappings, setMentionMappings] = useState<Record<string, string>>({}) // displayName -> userId
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const { sendMessage, sending } = useSendMessage(tripId)
  const { createNotifications } = useCreateMessageMentionNotifications()

  const characterLimit = 750
  const remainingChars = characterLimit - content.length

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    if (value.length <= characterLimit) {
      setContent(value)

      // Check for @ mentions
      const cursorPosition = e.target.selectionStart
      const textBeforeCursor = value.substring(0, cursorPosition)
      const lastAtIndex = textBeforeCursor.lastIndexOf("@")

      if (lastAtIndex !== -1) {
        const textAfterAt = textBeforeCursor.substring(lastAtIndex + 1)
        // Check if there's no space after @ (still typing mention)
        if (!textAfterAt.includes(" ") && !textAfterAt.includes("\n")) {
          setMentionQuery(textAfterAt)
          setMentionPosition(lastAtIndex)
          setShowMentions(true)
        } else {
          setShowMentions(false)
        }
      } else {
        setShowMentions(false)
      }
    }
  }

  const handleMentionSelect = useCallback(
    (attendee: { id: string; displayName: string }) => {
      const beforeMention = content.substring(0, mentionPosition)
      const afterMention = content.substring(mentionPosition + mentionQuery.length + 1) // +1 for @
      const mentionText = `@${attendee.displayName}`

      const newContent = beforeMention + mentionText + afterMention
      setContent(newContent)
      setShowMentions(false)
      setMentionQuery("")

      // Store the mapping of display name to user ID for later processing
      setMentionMappings((prev) => ({
        ...prev,
        [attendee.displayName]: attendee.id,
      }))

      // Focus back to textarea
      if (textareaRef.current) {
        textareaRef.current.focus()
        // Set cursor position after the mention
        const newCursorPosition = beforeMention.length + mentionText.length
        setTimeout(() => {
          textareaRef.current?.setSelectionRange(newCursorPosition, newCursorPosition)
        }, 0)
      }
    },
    [content, mentionPosition, mentionQuery]
  )

  const extractMentionedUserIds = (messageContent: string): string[] => {
    // Extract @displayName mentions and convert to user IDs
    const mentions: string[] = []

    // Look for all possible mentions in the current mappings
    Object.entries(mentionMappings).forEach(([displayName, userId]) => {
      const mentionPattern = new RegExp(
        `@${displayName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}(?=\\s|$|[^\\w])`,
        "g"
      )
      if (mentionPattern.test(messageContent)) {
        mentions.push(userId)
      }
    })

    return [...new Set(mentions)] // Remove duplicates
  }

  const convertMentionsForStorage = (messageContent: string): string => {
    // Convert @displayName to @[userId:displayName] format for storage
    let result = messageContent

    // Replace each mention found in mappings
    Object.entries(mentionMappings).forEach(([displayName, userId]) => {
      const mentionPattern = new RegExp(
        `@${displayName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}(?=\\s|$|[^\\w])`,
        "g"
      )
      result = result.replace(mentionPattern, `@[${userId}:${displayName}]`)
    })

    return result
  }

  const handleSend = async () => {
    if (!content.trim() || sending) return

    try {
      // Convert mentions to storage format
      const storageContent = convertMentionsForStorage(content.trim())

      // Extract mentioned user IDs
      const mentionedUserIds = extractMentionedUserIds(content)

      // Prepare message data - only include senderPhotoURL if it exists
      const messageData: MessageCreateData = {
        tripId,
        senderId: currentUser.id,
        senderName: currentUser.displayName,
        content: storageContent,
        mentionedUserIds,
        ...(currentUser.photoURL && { senderPhotoURL: currentUser.photoURL }),
      }

      // Send the message
      await sendMessage(messageData)

      // Create notifications for mentioned users
      if (mentionedUserIds.length > 0) {
        await createNotifications(
          mentionedUserIds,
          tripId,
          tripName,
          currentUser.displayName,
          currentUser.id,
          currentUser.photoURL // This can be undefined, the service will handle it
        )
      }

      // Clear the input
      setContent("")
      setShowMentions(false)
      setMentionQuery("")
      setMentionMappings({})
    } catch (error) {
      console.error("Error sending message:", error)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }

    if (e.key === "Escape") {
      setShowMentions(false)
    }

    // Handle backspace for mention deletion
    if (e.key === "Backspace") {
      const textarea = e.target as HTMLTextAreaElement
      const cursorPosition = textarea.selectionStart
      const textBeforeCursor = content.substring(0, cursorPosition)

      // Check if cursor is right after a mention (@displayName)
      // Look for any mention from our mappings that ends at the cursor position
      let mentionToDelete: { displayName: string; start: number; end: number } | null = null

      for (const displayName of Object.keys(mentionMappings)) {
        const mentionText = `@${displayName}`
        if (textBeforeCursor.endsWith(mentionText)) {
          mentionToDelete = {
            displayName,
            start: cursorPosition - mentionText.length,
            end: cursorPosition,
          }
          break // Found a match, no need to continue
        }
      }

      if (mentionToDelete) {
        e.preventDefault()
        const newContent =
          content.substring(0, mentionToDelete.start) + content.substring(mentionToDelete.end)
        setContent(newContent)

        // Remove from mention mappings
        setMentionMappings((prev) => {
          const newMappings = { ...prev }
          delete newMappings[mentionToDelete!.displayName]
          return newMappings
        })

        // Set cursor position
        setTimeout(() => {
          textarea.setSelectionRange(mentionToDelete!.start, mentionToDelete!.start)
        }, 0)
      }
    }
  }

  const filteredAttendees = attendees.filter(
    (attendee) =>
      attendee.displayName.toLowerCase().includes(mentionQuery.toLowerCase()) &&
      attendee.id !== currentUser.id // Don't show current user in mentions
  )

  // If user can't send messages, show appropriate prompt
  if (!canSendMessages) {
    // Check if trip is completed
    if (tripStatus === "completed") {
      return (
        <div className="bg-muted/30 rounded-lg p-4 text-center space-y-3">
          <div className="flex items-center justify-center gap-2 text-muted-foreground">
            <span className="text-sm font-medium">Trip Completed</span>
          </div>
          <p className="text-xs text-muted-foreground">
            This trip has been completed. Messaging is no longer available, but you can still view
            the chat history.
          </p>
        </div>
      )
    }

    // Otherwise, show subscription upgrade prompt
    if (!hasTripChat) {
      return (
        <div className="bg-muted/30 rounded-lg p-4 text-center space-y-3">
          <div className="flex items-center justify-center gap-2 text-muted-foreground">
            <Crown className="h-4 w-4 text-yellow-500" />
            <span className="text-sm font-medium">Trip Chat is a Pro Feature</span>
          </div>
          <p className="text-xs text-muted-foreground">
            Upgrade to Pro to send messages and stay connected with your squad during trips.
          </p>
          <Button
            onClick={() => router.push("/settings?tab=billing")}
            size="sm"
            className="bg-gradient-to-r from-[#FFD54F] to-[#FFC107] hover:from-[#FFCC02] hover:to-[#FF8F00] text-black font-medium"
          >
            <Crown className="h-3 w-3 mr-1" />
            Upgrade to Pro
          </Button>
        </div>
      )
    }
  }

  return (
    <div className="relative">
      {/* User Mention Dropdown */}
      {showMentions && filteredAttendees.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2">
          <UserMention
            attendees={filteredAttendees}
            onSelect={handleMentionSelect}
            query={mentionQuery}
          />
        </div>
      )}

      <div className="space-y-2">
        {/* Textarea and Button Row */}
        <div className="flex gap-2 items-stretch">
          <Textarea
            ref={textareaRef}
            value={content}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type a message... Use @ to mention someone"
            className="flex-1 min-h-[60px] max-h-[120px] resize-none"
            disabled={sending}
          />
          <Button
            onClick={handleSend}
            disabled={!content.trim() || sending}
            className="shrink-0 h-[60px] w-[60px] rounded-md"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {/* Character Count and Mentions Row */}
        <div className="flex justify-between items-center">
          <span
            className={`text-xs ${remainingChars < 50 ? "text-destructive" : "text-muted-foreground"}`}
          >
            {remainingChars} characters remaining
          </span>
          {showMentions && (
            <span className="text-xs text-muted-foreground">Press Esc to close mentions</span>
          )}
        </div>
      </div>
    </div>
  )
}
