"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MessageCircle, Crown, Sparkles } from "lucide-react"
import { useRouter } from "next/navigation"

interface ChatUpgradePromptProps {
  tripName: string
}

export function ChatUpgradePrompt({ tripName }: ChatUpgradePromptProps) {
  const router = useRouter()

  const handleUpgrade = () => {
    router.push("/settings?tab=billing")
  }

  return (
    <div className="h-full flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto text-center">
        <CardHeader className="pb-4">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <MessageCircle className="h-16 w-16 text-muted-foreground" />
              <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-1">
                <Crown className="h-4 w-4 text-white" />
              </div>
            </div>
          </div>
          <CardTitle className="text-xl font-semibold">Trip Chat is a Pro Feature</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            Stay connected with your squad during <strong>{tripName}</strong> with real-time
            messaging, photo sharing, and @mentions.
          </p>

          <div className="bg-muted/30 rounded-lg p-4 space-y-2">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-yellow-500" />
              Pro Features Include:
            </h4>
            <ul className="text-sm text-muted-foreground space-y-1 text-left">
              <li>• Real-time trip messaging</li>
              <li>• Photo and file sharing</li>
              <li>• @mention notifications</li>
              <li>• 5 concurrent squads</li>
              <li>• 3 trips per squad</li>
            </ul>
          </div>

          <Button
            onClick={handleUpgrade}
            className="w-full bg-gradient-to-r from-[#FFD54F] to-[#FFC107] hover:from-[#FFCC02] hover:to-[#FF8F00] text-white font-medium"
          >
            <Crown className="h-4 w-4 mr-2" />
            Upgrade to Pro
          </Button>

          <p className="text-xs text-muted-foreground">Starting at $7.99/month • Cancel anytime</p>
        </CardContent>
      </Card>
    </div>
  )
}
