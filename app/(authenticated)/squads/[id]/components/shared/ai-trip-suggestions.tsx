"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeSquad } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAIUsage } from "@/lib/domains/user-ai-usage/user-ai-usage.realtime.hooks"
import { AIUsageCategory } from "@/lib/domains/user-ai-usage/user-ai-usage.types"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription/user-subscription.hooks"

import { AIUsageWarning } from "@/components/ai-usage-warning"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { RefreshCw, Sparkles } from "lucide-react"
import { TripSuggestionDetails } from "./trip-suggestion-details"
import { useAITripSuggestionsWithLocation } from "@/lib/domains/ai-suggestions/ai-suggestions-trips-with-location.hooks"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"
import { EnhancedSuggestionCard } from "./enhanced-suggestion-card"
import { LocationPreferenceToggle } from "./location-preference-toggle"
import { LocationSetupDialog } from "./location-setup-dialog"

interface AiTripSuggestionsProps {
  squadId?: string
  onError?: (error: Error) => void
}

export function AiTripSuggestions({ squadId }: AiTripSuggestionsProps) {
  const params = useParams()
  const id = squadId || (params.id as string)
  const currentUser = useUser()
  const isSubscribed = useIsUserSubscribed()
  const { getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)

  // Get real-time squad data
  const { squad } = useRealtimeSquad(id)

  // Use the new AI trip suggestions hook with location support
  const {
    suggestions,
    loading,
    error,
    usageError,
    suggestionsLoaded,
    usingCachedSuggestions,
    locationPreference,
    hasUserLocation,
    loadSuggestions,
    setLocationPreference,
  } = useAITripSuggestionsWithLocation(squadId)

  const [refreshing, setRefreshing] = useState(false)
  const [localUsageCount, setLocalUsageCount] = useState<number>(0)
  const [showLocationDialog, setShowLocationDialog] = useState(false)
  const [dialogPreferenceType, setDialogPreferenceType] = useState<"local" | "national">("local")

  // State for suggestion details modal
  const [selectedSuggestion, setSelectedSuggestion] = useState<CachedTripSuggestion | null>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)

  // Update local usage count when real-time data changes
  useEffect(() => {
    const categoryUsage = getCategoryUsage(AIUsageCategory.TRIP)
    if (categoryUsage) {
      setLocalUsageCount(categoryUsage.count)
    }
  }, [getCategoryUsage])

  // Handle refresh button click
  async function handleRefresh(): Promise<void> {
    setRefreshing(true)
    await loadSuggestions(true)
    setRefreshing(false)
  }

  // Wrapper function for button click events
  const handleFetchClick = async () => {
    const wasUsingCached = usingCachedSuggestions
    await loadSuggestions(false)

    // If we weren't using cached suggestions, increment the local counter immediately
    // This provides immediate UI feedback while waiting for the real-time update
    if (!wasUsingCached && !isSubscribed) {
      setLocalUsageCount((prev) => prev + 1)
    }
  }

  // Function to check if we're in a solo squad (just the current user)
  const isSoloSquad = (): boolean => {
    return !!(squad?.memberCount === 1 && currentUser && squad?.leaderId === currentUser.uid)
  }

  const handleViewDetails = (suggestion: CachedTripSuggestion) => {
    setSelectedSuggestion(suggestion)
    setDetailsOpen(true)
  }

  const handleLocationSetupPrompt = () => {
    // Determine which preference type was clicked based on current state
    const preferenceType = locationPreference === "local" ? "local" : "national"
    setDialogPreferenceType(preferenceType)
    setShowLocationDialog(true)
  }

  return (
    <>
      {selectedSuggestion && (
        <TripSuggestionDetails
          suggestion={selectedSuggestion}
          open={detailsOpen}
          onOpenChange={setDetailsOpen}
          squadId={id}
        />
      )}
      {usageError && usageError.show && (
        <AIUsageWarning
          errorType={usageError.errorType}
          usageData={usageError.usageData}
          onClose={() => {}}
          className="mb-4"
        />
      )}
      <Card>
        <CardHeader className="space-y-3">
          {/* Main header row with title and controls */}
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
            <div className="flex-1">
              <CardTitle>Trip Ideas</CardTitle>
              <CardDescription>
                {usingCachedSuggestions
                  ? "Cached trip recommendations based on "
                  : "AI-powered recommendations based on "}
                {isSoloSquad() ? "your" : "squad"} preferences
              </CardDescription>
            </div>

            {/* Controls on the right */}
            {suggestionsLoaded && (
              <div className="flex items-center gap-2">
                {!isSubscribed && (
                  <span className="text-xs text-muted-foreground">
                    {localUsageCount}/{getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                  </span>
                )}
                {usingCachedSuggestions && (
                  <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md">
                    Cached
                  </span>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleRefresh}
                  disabled={loading || refreshing}
                >
                  <RefreshCw className={`h-4 w-4 ${loading || refreshing ? "animate-spin" : ""}`} />
                </Button>
              </div>
            )}
          </div>

          {/* Multi-member squad hint */}
          {squad &&
            squad.memberCount &&
            squad.memberCount > 1 &&
            locationPreference !== "global" && (
              <div className="text-xs text-muted-foreground">
                💡 Global is recommended for multi-member squads
              </div>
            )}
        </CardHeader>
        <CardContent>
          {/* Location Setup Message for Local/National without user location */}
          {(locationPreference === "local" || locationPreference === "national") &&
            !hasUserLocation && (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="mb-4">
                  <div className="text-lg font-medium mb-2">Setup your location</div>
                  <div className="text-muted-foreground mb-4">
                    To get {locationPreference} suggestions, please configure your location in
                    settings.
                  </div>
                </div>
                <Button onClick={() => window.open("/settings", "_blank")} variant="default">
                  Go to Profile Settings
                </Button>
              </div>
            )}

          {/* Regular content when location is available or global is selected */}
          {(locationPreference === "global" ||
            ((locationPreference === "local" || locationPreference === "national") &&
              hasUserLocation)) && (
            <>
              {!suggestionsLoaded && !loading && !error && (
                <div className="flex flex-col items-center justify-center py-8 gap-4">
                  {/* Location Preference Toggle - above the button */}
                  <div className="w-full max-w-sm">
                    <LocationPreferenceToggle
                      value={locationPreference}
                      onChange={(preference) => {
                        setLocationPreference(preference)
                        // Auto-load suggestions when preference changes if suggestions are already loaded
                        if (suggestionsLoaded) {
                          loadSuggestions(false, preference)
                        }
                      }}
                      disabled={loading}
                      hasUserLocation={hasUserLocation}
                      onLocationSetupPrompt={handleLocationSetupPrompt}
                    />
                  </div>

                  {/* Generate Trip Ideas Button - same width as toggle */}
                  <div className="w-full max-w-sm">
                    <Button onClick={handleFetchClick} disabled={loading} className="w-full">
                      {loading ? (
                        <div className="flex items-center justify-center gap-2">
                          <RefreshCw className="h-4 w-4 animate-spin" />
                          <span>Loading...</span>
                        </div>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          {!isSubscribed && (
                            <span className="mr-2 text-xs">
                              {localUsageCount}/{getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                            </span>
                          )}
                          {currentUser?.uid && suggestions.length > 0
                            ? "Show Cached Trip Ideas"
                            : "Generate Trip Ideas"}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
              {loading && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(3)].map((_, i) => (
                    <Card key={i}>
                      <div className="aspect-video relative">
                        <Skeleton className="absolute inset-0" />
                      </div>
                      <CardContent className="pt-4">
                        <Skeleton className="h-6 w-3/4 mb-2" />
                        <Skeleton className="h-4 w-1/2 mb-4" />
                        <div className="flex gap-2 mb-4">
                          <Skeleton className="h-5 w-16" />
                          <Skeleton className="h-5 w-16" />
                        </div>
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-4 w-2/3" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </>
          )}

          {/* Error and suggestions sections - only show when location is available or global */}
          {(locationPreference === "global" ||
            ((locationPreference === "local" || locationPreference === "national") &&
              hasUserLocation)) && (
            <>
              {error && (
                <div className="text-center py-8">
                  <p className="text-red-500 mb-4">{error}</p>
                  <Button onClick={handleFetchClick} disabled={loading}>
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading...</span>
                      </div>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        {!isSubscribed && (
                          <span className="mr-2 text-xs">
                            {localUsageCount}/{getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                          </span>
                        )}
                        Try Again
                      </>
                    )}
                  </Button>
                </div>
              )}
              {suggestionsLoaded && !loading && !error && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {suggestions.length > 0 ? (
                    suggestions.map((suggestion, index) => (
                      <EnhancedSuggestionCard
                        key={index}
                        suggestion={suggestion}
                        index={index}
                        onViewDetails={handleViewDetails}
                      />
                    ))
                  ) : (
                    <div className="col-span-full text-center py-8">
                      <p className="text-muted-foreground mb-4">No trip suggestions available</p>
                      <Button onClick={handleFetchClick} disabled={loading}>
                        {loading ? (
                          <div className="flex items-center justify-center gap-2">
                            <RefreshCw className="h-4 w-4 animate-spin" />
                            <span>Loading...</span>
                          </div>
                        ) : (
                          <>
                            <Sparkles className="h-4 w-4 mr-2" />
                            {!isSubscribed && (
                              <span className="mr-2 text-xs">
                                {localUsageCount}/
                                {getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                              </span>
                            )}
                            Generate Trip Ideas
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Location Setup Dialog */}
      <LocationSetupDialog
        open={showLocationDialog}
        onOpenChange={setShowLocationDialog}
        preferenceType={dialogPreferenceType}
      />
    </>
  )
}
