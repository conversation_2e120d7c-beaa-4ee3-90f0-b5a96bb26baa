"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { ArrowLef<PERSON>, ArrowR<PERSON>, Upload, X } from "lucide-react"
import { MonthSelector } from "@/components/month-selector"
import { LocationInputWithGeolocation } from "@/components/location-input-with-geolocation"
import { toast } from "@/components/ui/use-toast"
import { createUserWithProfileAction } from "../actions/create-user-with-profile"
import { Logo } from "@/components/ui/logo"
import {
  ALLOWED_TRAVEL_TYPES,
  ALLOWED_AVAILABILITY_PREFERENCES,
  ALLOWED_TRAVEL_GROUP_PREFERENCES,
} from "@/lib/constants/travel-types"
// import { SUBSCRIPTION_LIMITS, AI_USAGE_LIMITS } from "@/lib/domains/user-ai-usage/user-ai-usage.types"

export function SignupForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [step, setStep] = useState(1)
  const [selectedMonths, setSelectedMonths] = useState<string[]>([])
  const [selectedTravelPreferences, setSelectedTravelPreferences] = useState<string[]>([])
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>([])
  const [selectedTravelGroups, setSelectedTravelGroups] = useState<string[]>([])
  const [budget, setBudget] = useState("mid-range")
  const [location, setLocation] = useState("")
  const [locationPlaceId, setLocationPlaceId] = useState<string | undefined>()
  const [loading, setLoading] = useState(false)
  const [callbackUrl, setCallbackUrl] = useState<string | null>(null)
  const [invitedEmail, setInvitedEmail] = useState<string | null>(null)
  const [referralCode, setReferralCode] = useState<string>("")
  const [profilePicture, setProfilePicture] = useState<File | null>(null)
  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null)
  const totalSteps = 4

  // Extract callback URL, invited email, and referral code from search params if they exist
  useEffect(() => {
    const callback = searchParams.get("callback")
    if (callback) {
      setCallbackUrl(callback)
    }

    const email = searchParams.get("invited_email")
    if (email) {
      setInvitedEmail(email)
      // Pre-fill the email field
      setFormData((prev) => ({ ...prev, email }))
    }

    const referral = searchParams.get("referral_code")
    if (referral) {
      setReferralCode(referral)
    }
  }, [searchParams])

  // Form data
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    name: "",
    bio: "",
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev) => ({ ...prev, [id]: value }))
  }

  const handleProfilePictureSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Please select a JPEG, PNG, or WebP image.",
        variant: "destructive",
      })
      return
    }

    // Validate file size (1MB)
    const maxSize = 1 * 1024 * 1024
    if (file.size > maxSize) {
      toast({
        title: "File too large",
        description: "Please select an image smaller than 1MB.",
        variant: "destructive",
      })
      return
    }

    setProfilePicture(file)

    // Create preview URL
    const reader = new FileReader()
    reader.onload = (e) => {
      setProfilePicturePreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const removeProfilePicture = () => {
    setProfilePicture(null)
    setProfilePicturePreview(null)
    // Reset file input
    const fileInput = document.getElementById("profilePicture") as HTMLInputElement
    if (fileInput) {
      fileInput.value = ""
    }
  }

  const togglePreference = (
    preference: string,
    setter: React.Dispatch<React.SetStateAction<string[]>>,
    current: string[]
  ) => {
    if (current.includes(preference)) {
      setter(current.filter((p) => p !== preference))
    } else {
      setter([...current, preference])
    }
  }

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleSignup = async () => {
    if (!formData.email || !formData.password || !formData.name) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      // Prepare profile picture form data if a file is selected
      let profilePictureFormData: FormData | undefined
      if (profilePicture) {
        profilePictureFormData = new FormData()
        profilePictureFormData.append("file", profilePicture)
      }

      // Create user with profile using server action
      const result = await createUserWithProfileAction(
        {
          email: formData.email,
          password: formData.password,
          name: formData.name,
          bio: formData.bio,
          location: location || undefined,
          locationPlaceId: locationPlaceId || undefined,
          selectedTravelPreferences,
          budget,
          selectedAvailability,
          selectedMonths,
          selectedTravelGroups,
          referralCode: referralCode.trim() || undefined,
        },
        profilePictureFormData
      )

      if (result.success) {
        // Show success message
        toast({
          title: "Account created successfully!",
          description: "Please log in with your new credentials.",
        })

        // Redirect to login page with callback if it exists
        if (callbackUrl) {
          router.push(`/login?callback=${encodeURIComponent(callbackUrl)}`)
        } else {
          router.push(result.redirectUrl || "/login?message=account_created")
        }
      } else {
        toast({
          title: "Error creating account",
          description: result.error || "Something went wrong. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error("Signup error:", error)
      toast({
        title: "Error creating account",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/">
            <Logo />
          </Link>
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Create your account</CardTitle>
            <CardDescription>
              Step {step} of {totalSteps}: {getStepDescription(step)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {step === 1 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={!!invitedEmail}
                  />
                  {invitedEmail && (
                    <p className="text-xs text-muted-foreground mt-1">
                      This email is pre-filled from your invitation and cannot be changed.
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="referralCode">Referral Code (Optional)</Label>
                  <Input
                    id="referralCode"
                    placeholder="Enter referral code"
                    value={referralCode}
                    onChange={(e) => setReferralCode(e.target.value.toUpperCase())}
                    maxLength={8}
                  />
                  {referralCode && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Using referral code: {referralCode} (Length: {referralCode.length})
                    </p>
                  )}
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Travel Preferences</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_TRAVEL_TYPES.map((pref) => (
                      <Button
                        key={pref}
                        variant={selectedTravelPreferences.includes(pref) ? "default" : "outline"}
                        className="justify-start"
                        onClick={() =>
                          togglePreference(
                            pref,
                            setSelectedTravelPreferences,
                            selectedTravelPreferences
                          )
                        }
                      >
                        {pref}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget">Typical Budget</Label>
                  <select
                    id="budget"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={budget}
                    onChange={(e) => setBudget(e.target.value)}
                  >
                    <option value="budget-friendly">Budget-friendly</option>
                    <option value="mid-range">Mid-range</option>
                    <option value="luxury">Luxury</option>
                  </select>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Availability</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_AVAILABILITY_PREFERENCES.map((avail) => (
                      <Button
                        key={avail}
                        variant={selectedAvailability.includes(avail) ? "default" : "outline"}
                        className="justify-start"
                        onClick={() =>
                          togglePreference(avail, setSelectedAvailability, selectedAvailability)
                        }
                      >
                        {avail}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Preferred Travel Seasons</Label>
                  <MonthSelector
                    selectedMonths={selectedMonths}
                    onChange={setSelectedMonths}
                    onConfirm={() => {
                      // Optional: Add any additional logic when user confirms selection
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Travel Group</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_TRAVEL_GROUP_PREFERENCES.map((group) => (
                      <Button
                        key={group}
                        variant={selectedTravelGroups.includes(group) ? "default" : "outline"}
                        className="justify-start"
                        onClick={() =>
                          togglePreference(group, setSelectedTravelGroups, selectedTravelGroups)
                        }
                      >
                        {group}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {step === 4 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Profile Picture (Optional)</Label>
                  {profilePicturePreview ? (
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <img
                          src={
                            profilePicturePreview && profilePicturePreview.trim() !== ""
                              ? profilePicturePreview
                              : "/placeholder.svg"
                          }
                          alt="Profile preview"
                          className="w-20 h-20 rounded-full object-cover border-2 border-border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                          onClick={removeProfilePicture}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{profilePicture?.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {profilePicture && (profilePicture.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center p-6 border-2 border-dashed rounded-md">
                      <div className="text-center">
                        <Upload className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                        <div className="mt-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById("profilePicture")?.click()}
                          >
                            Upload Photo
                          </Button>
                          <input
                            id="profilePicture"
                            type="file"
                            accept="image/jpeg,image/jpg,image/png,image/webp"
                            onChange={handleProfilePictureSelect}
                            className="hidden"
                          />
                        </div>
                        <p className="mt-1 text-xs text-muted-foreground">
                          JPEG, PNG, WebP up to 1MB
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                <LocationInputWithGeolocation
                  value={location}
                  onChange={(value, placeId) => {
                    setLocation(value)
                    setLocationPlaceId(placeId)
                  }}
                  placeholder="Where are you based?"
                  required={false}
                  allowUnauthenticated={true}
                  label="Location (Optional)"
                  showGeolocationButton={true}
                />
                <div className="space-y-2">
                  <Label htmlFor="bio">Bio (Optional)</Label>
                  <textarea
                    id="bio"
                    rows={3}
                    placeholder="Tell your friends a bit about yourself..."
                    className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={formData.bio}
                    onChange={handleInputChange}
                  ></textarea>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={prevStep} disabled={step === 1}>
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            {step < totalSteps ? (
              <Button onClick={nextStep}>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button onClick={handleSignup} disabled={loading}>
                {loading ? "Creating Account..." : "Complete Setup"}
              </Button>
            )}
          </CardFooter>
          <div className="text-center text-sm pb-4">
            Already have an account?{" "}
            <Link href="/login" className="text-primary hover:underline">
              Log In
            </Link>
          </div>
        </Card>
      </main>
    </div>
  )
}

function getStepDescription(step: number) {
  switch (step) {
    case 1:
      return "Account Information"
    case 2:
      return "Travel Preferences"
    case 3:
      return "Availability & Group Type"
    case 4:
      return "Profile Setup"
    default:
      return ""
  }
}
